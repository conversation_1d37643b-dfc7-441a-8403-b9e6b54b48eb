# 科学计算器键盘布局

## 新的7行5列网格布局

科学计算器现在采用7行5列的网格布局，具有清晰的视觉分隔和一致的颜色方案。

### 键盘布局

```
┌─────┬─────┬─────┬─────┬─────┐
│  C  │ deg │ fx  │ 1/x │  e  │  第一行
├─────┼─────┼─────┼─────┼─────┤
│ sin │  √  │ xʸ  │ x!  │  π  │  第二行
├─────┼─────┼─────┼─────┼─────┤
│ cos │  (  │  )  │ DEL │  ÷  │  第三行
├─────┼─────┼─────┼─────┼─────┤
│ tan │  7  │  8  │  9  │  ×  │  第四行
├─────┼─────┼─────┼─────┼─────┤
│ cot │  4  │  5  │  6  │  -  │  第五行
├─────┼─────┼─────┼─────┼─────┤
│ ln  │  1  │  2  │  3  │  +  │  第六行
├─────┼─────┼─────┼─────┼─────┤
│ lg  │  %  │  0  │  .  │  =  │  第七行
└─────┴─────┴─────┴─────┴─────┘
```

### 颜色方案

- **数字键 (0-9)**: 白底黑字
- **小数点 (.)**: 白底黑字
- **等号 (=)**: 黄底白字
- **基础运算符 (C、DEL、÷、×、-、+、%)**: 白底黄字
- **科学函数**: 白底灰字

### 功能说明

| 按钮 | 功能 | 颜色 |
|------|------|------|
| C | 清除所有 | 白底黄字 |
| deg/rad | 角度模式切换 | 白底灰字 |
| fx | 预留功能 | 白底灰字 |
| 1/x | 倒数 | 白底灰字 |
| e | 自然常数e | 白底灰字 |
| sin | 正弦函数 | 白底灰字 |
| √ | 平方根 | 白底灰字 |
| xʸ | 幂运算 | 白底灰字 |
| x! | 阶乘 | 白底灰字 |
| π | 圆周率π | 白底灰字 |
| cos | 余弦函数 | 白底灰字 |
| ( | 左括号 | 白底灰字 |
| ) | 右括号 | 白底灰字 |
| DEL | 删除 | 白底黄字 |
| ÷ | 除法 | 白底黄字 |
| tan | 正切函数 | 白底灰字 |
| 7-9 | 数字输入 | 白底黑字 |
| × | 乘法 | 白底黄字 |
| cot | 余切函数 | 白底灰字 |
| 4-6 | 数字输入 | 白底黑字 |
| - | 减法 | 白底黄字 |
| ln | 自然对数 | 白底灰字 |
| 1-3 | 数字输入 | 白底黑字 |
| + | 加法 | 白底黄字 |
| lg | 常用对数 | 白底灰字 |
| % | 百分比 | 白底黄字 |
| 0 | 数字零 | 白底黑字 |
| . | 小数点 | 白底黑字 |
| = | 计算结果 | 黄底白字 |

### 独立状态管理

科学计算器现在使用独立的 `ScientificCalculatorProvider`，与标准计算器完全分离：

- **独立的表达式和显示状态**
- **角度模式切换** (度/弧度)
- **完整的科学函数支持**
- **与标准计算器互不干扰**

### 科学函数特性

- **三角函数**: sin, cos, tan, cot (支持度/弧度模式)
- **对数函数**: ln (自然对数), lg (常用对数)
- **幂运算**: xʸ (任意幂), √ (平方根)
- **特殊函数**: x! (阶乘), 1/x (倒数)
- **数学常数**: π (圆周率), e (自然常数)

这种布局提供了专业的科学计算器体验，具有清晰的功能分区和直观的操作逻辑。
