# 标准计算器键盘布局

## 新的5行4列网格布局

标准计算器现在采用5行4列的网格布局，具有清晰的视觉分隔和一致的颜色方案。

### 键盘布局

```
┌─────┬─────┬─────┬─────┐
│  C  │  ÷  │  ×  │ DEL │  第一行
├─────┼─────┼─────┼─────┤
│  7  │  8  │  9  │  -  │  第二行
├─────┼─────┼─────┼─────┤
│  4  │  5  │  6  │  +  │  第三行
├─────┼─────┼─────┼─────┤
│  1  │  2  │  3  │ ( ) │  第四行
├─────┼─────┼─────┼─────┤
│  %  │  0  │  .  │  =  │  第五行
└─────┴─────┴─────┴─────┘
```

### 颜色方案

- **数字键 (0-9)**: 白底黑字
- **小数点 (.)**: 白底黑字
- **等号 (=)**: 黄底白字
- **其他功能键**: 白底黄字

### 特殊功能

#### 智能括号 `( )`
- 合并的括号按钮会根据当前表达式智能选择输入左括号或右括号
- 逻辑：
  - 如果表达式为空或最后一个字符是运算符 → 输入 `(`
  - 如果有未配对的左括号且最后一个字符不是运算符 → 输入 `)`

#### 网格视觉效果
- 所有按钮都有细边框，形成清晰的网格效果
- 按钮间距减小，提供更紧凑的布局
- 统一的按钮高度和宽度

### 功能说明

| 按钮 | 功能 | 颜色 |
|------|------|------|
| C | 清除所有 | 白底黄字 |
| ÷ | 除法 | 白底黄字 |
| × | 乘法 | 白底黄字 |
| DEL | 删除最后一个字符 | 白底黄字 |
| 7-9 | 数字输入 | 白底黑字 |
| - | 减法 | 白底黄字 |
| 4-6 | 数字输入 | 白底黑字 |
| + | 加法 | 白底黄字 |
| 1-3 | 数字输入 | 白底黑字 |
| ( ) | 智能括号 | 白底黄字 |
| % | 百分比 | 白底黄字 |
| 0 | 数字零 | 白底黑字 |
| . | 小数点 | 白底黑字 |
| = | 计算结果 | 黄底白字 |

这种布局提供了更好的用户体验，具有清晰的视觉层次和直观的操作逻辑。
