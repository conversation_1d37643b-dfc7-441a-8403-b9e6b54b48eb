PODS:
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - OrderedSet

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: 97215cf7d4677db55df76782dbd2930c5e1c1ea0
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.16.2
