import 'package:flutter/material.dart';

class CalculatorDisplay extends StatelessWidget {
  final String display;
  final String expression;

  const CalculatorDisplay({
    super.key,
    required this.display,
    required this.expression,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Expression display (smaller text)
          if (expression.isNotEmpty && expression != display)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(bottom: 8),
              child: Text(
                expression,
                style: TextStyle(
                  fontSize: 18,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  fontFamily: 'monospace',
                ),
                textAlign: TextAlign.right,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          
          // Main display (larger text)
          SizedBox(
            width: double.infinity,
            child: Text(
              display,
              style: TextStyle(
                fontSize: _getFontSize(display),
                fontWeight: FontWeight.w300,
                color: Theme.of(context).colorScheme.onSurface,
                fontFamily: 'monospace',
              ),
              textAlign: TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  double _getFontSize(String text) {
    if (text.length <= 8) {
      return 48;
    } else if (text.length <= 12) {
      return 36;
    } else if (text.length <= 16) {
      return 28;
    } else {
      return 24;
    }
  }
}
