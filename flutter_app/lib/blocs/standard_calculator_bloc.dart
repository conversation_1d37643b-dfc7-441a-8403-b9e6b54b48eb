import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:math_expressions/math_expressions.dart';
import 'dart:math' as math;
import 'calculator_event.dart';
import 'calculator_state.dart';

class StandardCalculatorBloc extends Bloc<CalculatorEvent, CalculatorState> {
  StandardCalculatorBloc() : super(const CalculatorDisplayState(display: '0', expression: '')) {
    on<NumberPressed>(_onNumberPressed);
    on<OperatorPressed>(_onOperatorPressed);
    on<EqualsPressed>(_onEqualsPressed);
    on<ClearPressed>(_onClearPressed);
    on<DeletePressed>(_onDeletePressed);
    on<ToggleSignPressed>(_onToggleSignPressed);
    on<DecimalPressed>(_onDecimalPressed);
  }

  // 安全地获取当前的标准计算器状态
  CalculatorDisplayState _getCurrentState() {
    return state is CalculatorDisplayState
        ? state as CalculatorDisplayState
        : const CalculatorDisplayState(display: '0', expression: '');
  }

  void _onNumberPressed(NumberPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    
    if (currentState.shouldResetDisplay) {
      emit(CalculatorDisplayState(
        display: event.number,
        expression: event.number,
        shouldResetDisplay: false,
      ));
    } else {
      if (currentState.display == '0') {
        emit(CalculatorDisplayState(
          display: event.number,
          expression: event.number,
        ));
      } else {
        emit(CalculatorDisplayState(
          display: currentState.display + event.number,
          expression: currentState.expression + event.number,
        ));
      }
    }
  }

  void _onOperatorPressed(OperatorPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    
    String newExpression = currentState.expression;
    
    // Replace the last character if it's an operator
    if (newExpression.isNotEmpty && _isOperator(newExpression[newExpression.length - 1])) {
      newExpression = newExpression.substring(0, newExpression.length - 1) + event.operator;
    } else {
      newExpression += event.operator;
    }
    
    emit(CalculatorDisplayState(
      display: newExpression,
      expression: newExpression,
      shouldResetDisplay: false,
    ));
  }

  void _onEqualsPressed(EqualsPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    
    try {
      if (currentState.expression.isEmpty) return;

      // Handle power operations first
      if (currentState.expression.contains('^')) {
        _calculatePower(currentState.expression, emit);
        return;
      }

      // Replace display operators with math expression operators
      String mathExpression = currentState.expression
          .replaceAll('×', '*')
          .replaceAll('÷', '/');

      Parser parser = Parser();
      Expression exp = parser.parse(mathExpression);
      ContextModel cm = ContextModel();
      double result = exp.evaluate(EvaluationType.REAL, cm);

      // Format result
      String displayResult;
      if (result == result.toInt()) {
        displayResult = result.toInt().toString();
      } else {
        displayResult = result.toString();
      }

      emit(CalculatorDisplayState(
        display: displayResult,
        expression: displayResult,
        shouldResetDisplay: true,
      ));
    } catch (e) {
    emit(const CalculatorDisplayState(display: '0', expression: ''));
    }
  }

  void _onClearPressed(ClearPressed event, Emitter<CalculatorState> emit) {
    emit(const CalculatorDisplayState(display: '0', expression: ''));
  }

  void _onDeletePressed(DeletePressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    
    if (currentState.shouldResetDisplay) {
      emit(const CalculatorDisplayState(display: '0', expression: ''));
      return;
    }
    
    if (currentState.expression.isNotEmpty) {
      String newExpression = currentState.expression.substring(0, currentState.expression.length - 1);
      String newDisplay = newExpression.isEmpty ? '0' : newExpression;
      
      emit(CalculatorDisplayState(
        display: newDisplay,
        expression: newExpression,
      ));
    }
  }

  void _onToggleSignPressed(ToggleSignPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    
    if (currentState.display == '0' || currentState.display == 'Error') return;
    
    String newDisplay, newExpression;
    if (currentState.display.startsWith('-')) {
      newDisplay = currentState.display.substring(1);
      newExpression = currentState.expression.substring(1);
    } else {
      newDisplay = '-${currentState.display}';
      newExpression = '-${currentState.expression}';
    }
    
    emit(CalculatorDisplayState(
      display: newDisplay,
      expression: newExpression,
    ));
  }

  void _onDecimalPressed(DecimalPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    
    if (currentState.shouldResetDisplay) {
      emit(const CalculatorDisplayState(
        display: '0.',
        expression: '0.',
        shouldResetDisplay: false,
      ));
    } else {
      if (!currentState.display.contains('.')) {
        emit(CalculatorDisplayState(
          display: currentState.display + '.',
          expression: currentState.expression + '.',
        ));
      }
    }
  }

  bool _isOperator(String char) {
    return ['+', '-', '×', '÷', '*', '/'].contains(char);
  }

  void _calculatePower(String expression, Emitter<CalculatorState> emit) {
    try {
      List<String> parts = expression.split('^');
      if (parts.length == 2) {
        double base = double.parse(parts[0]);
        double exponent = double.parse(parts[1]);
        double result = math.pow(base, exponent).toDouble();
        
        String displayResult;
        if (result == result.toInt()) {
          displayResult = result.toInt().toString();
        } else {
          displayResult = result.toString();
        }
        
        emit(CalculatorDisplayState(
          display: displayResult,
          expression: displayResult,
          shouldResetDisplay: true,
        ));
      }
    } catch (e) {
      emit(const CalculatorDisplayState(display: '0', expression: ''));
    }
  }
}
