import 'package:equatable/equatable.dart';

abstract class CalculatorEvent extends Equatable {
  const CalculatorEvent();

  @override
  List<Object> get props => [];
}

class NumberPressed extends CalculatorEvent {
  final String number;

  const NumberPressed(this.number);

  @override
  List<Object> get props => [number];
}

class OperatorPressed extends CalculatorEvent {
  final String operator;

  const OperatorPressed(this.operator);

  @override
  List<Object> get props => [operator];
}

class EqualsPressed extends CalculatorEvent {
  const EqualsPressed();
}

class ClearPressed extends CalculatorEvent {
  const ClearPressed();
}

class DeletePressed extends CalculatorEvent {
  const DeletePressed();
}

class ToggleSignPressed extends CalculatorEvent {
  const ToggleSignPressed();
}

class DecimalPressed extends CalculatorEvent {
  const DecimalPressed();
}

// Scientific calculator specific events
class ScientificFunctionPressed extends CalculatorEvent {
  final String function;

  const ScientificFunctionPressed(this.function);

  @override
  List<Object> get props => [function];
}

class ToggleAngleModePressed extends CalculatorEvent {
  const ToggleAngleModePressed();
}

class ConstantPressed extends CalculatorEvent {
  final String constant;

  const ConstantPressed(this.constant);

  @override
  List<Object> get props => [constant];
}
