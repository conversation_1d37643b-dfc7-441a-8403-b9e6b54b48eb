import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:math_expressions/math_expressions.dart';
import 'dart:math' as math;
import 'calculator_event.dart';
import 'calculator_state.dart';

class ScientificCalculatorBloc extends Bloc<CalculatorEvent, CalculatorState> {
  ScientificCalculatorBloc()
      : super(const ScientificCalculatorState(display: '0', expression: '')) {
    on<NumberPressed>(_onNumberPressed);
    on<OperatorPressed>(_onOperatorPressed);
    on<EqualsPressed>(_onEqualsPressed);
    on<ClearPressed>(_onClearPressed);
    on<DeletePressed>(_onDeletePressed);
    on<ToggleSignPressed>(_onToggleSignPressed);
    on<DecimalPressed>(_onDecimalPressed);
    on<ScientificFunctionPressed>(_onScientificFunctionPressed);
    on<ToggleAngleModePressed>(_onToggleAngleModePressed);
    on<ConstantPressed>(_onConstantPressed);
  }

  // 安全地获取当前的科学计算器状态
  ScientificCalculatorState _getCurrentState() {
    return state is ScientificCalculatorState
        ? state as ScientificCalculatorState
        : const ScientificCalculatorState(display: '0', expression: '');
  }

  void _onNumberPressed(NumberPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    if (currentState.shouldResetDisplay) {
      emit(ScientificCalculatorState(
        display: event.number,
        expression: event.number,
        shouldResetDisplay: false,
        isDegreeMode: currentState.isDegreeMode,
      ));
    } else {
      if (currentState.display == '0') {
        emit(ScientificCalculatorState(
          display: event.number,
          expression: event.number,
          isDegreeMode: currentState.isDegreeMode,
        ));
      } else {
        emit(ScientificCalculatorState(
          display: currentState.display + event.number,
          expression: currentState.expression + event.number,
          isDegreeMode: currentState.isDegreeMode,
        ));
      }
    }
  }

  void _onOperatorPressed(
      OperatorPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    String newExpression = currentState.expression;

    // Replace the last character if it's an operator
    if (newExpression.isNotEmpty &&
        _isOperator(newExpression[newExpression.length - 1])) {
      newExpression =
          newExpression.substring(0, newExpression.length - 1) + event.operator;
    } else {
      newExpression += event.operator;
    }

    emit(ScientificCalculatorState(
      display: newExpression,
      expression: newExpression,
      shouldResetDisplay: false,
      isDegreeMode: currentState.isDegreeMode,
    ));
  }

  void _onEqualsPressed(EqualsPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    try {
      if (currentState.expression.isEmpty) return;

      // Handle power operations first
      if (currentState.expression.contains('^')) {
        _calculatePower(
            currentState.expression, emit, currentState.isDegreeMode);
        return;
      }

      // Replace display operators with math expression operators
      String mathExpression =
          currentState.expression.replaceAll('×', '*').replaceAll('÷', '/');

      Parser parser = Parser();
      Expression exp = parser.parse(mathExpression);
      ContextModel cm = ContextModel();
      double result = exp.evaluate(EvaluationType.REAL, cm);

      // Format result
      String displayResult;
      if (result == result.toInt()) {
        displayResult = result.toInt().toString();
      } else {
        displayResult = result.toString();
      }

      emit(ScientificCalculatorState(
        display: displayResult,
        expression: displayResult,
        shouldResetDisplay: true,
        isDegreeMode: currentState.isDegreeMode,
      ));
    } catch (e) {
      emit(ScientificCalculatorState(
        display: '0',
        expression: '',
        isDegreeMode: currentState.isDegreeMode,
      ));
    }
  }

  void _onClearPressed(ClearPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    emit(ScientificCalculatorState(
      display: '0',
      expression: '',
      isDegreeMode: currentState.isDegreeMode,
    ));
  }

  void _onDeletePressed(DeletePressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    if (currentState.shouldResetDisplay) {
      emit(ScientificCalculatorState(
        display: '0',
        expression: '',
        isDegreeMode: currentState.isDegreeMode,
      ));
      return;
    }

    if (currentState.expression.isNotEmpty) {
      String newExpression = currentState.expression
          .substring(0, currentState.expression.length - 1);
      String newDisplay = newExpression.isEmpty ? '0' : newExpression;

      emit(ScientificCalculatorState(
        display: newDisplay,
        expression: newExpression,
        isDegreeMode: currentState.isDegreeMode,
      ));
    }
  }

  void _onToggleSignPressed(
      ToggleSignPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    if (currentState.display == '0' || currentState.display == 'Error') return;

    String newDisplay, newExpression;
    if (currentState.display.startsWith('-')) {
      newDisplay = currentState.display.substring(1);
      newExpression = currentState.expression.substring(1);
    } else {
      newDisplay = '-${currentState.display}';
      newExpression = '-${currentState.expression}';
    }

    emit(ScientificCalculatorState(
      display: newDisplay,
      expression: newExpression,
      isDegreeMode: currentState.isDegreeMode,
    ));
  }

  void _onDecimalPressed(DecimalPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    if (currentState.shouldResetDisplay) {
      emit(ScientificCalculatorState(
        display: '0.',
        expression: '0.',
        shouldResetDisplay: false,
        isDegreeMode: currentState.isDegreeMode,
      ));
    } else {
      if (!currentState.display.contains('.')) {
        emit(ScientificCalculatorState(
          display: currentState.display + '.',
          expression: currentState.expression + '.',
          isDegreeMode: currentState.isDegreeMode,
        ));
      }
    }
  }

  void _onScientificFunctionPressed(
      ScientificFunctionPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    try {
      double currentValue = double.parse(currentState.display);
      double result;

      switch (event.function) {
        case 'sin':
          result = currentState.isDegreeMode
              ? math.sin(currentValue * math.pi / 180)
              : math.sin(currentValue);
          break;
        case 'cos':
          result = currentState.isDegreeMode
              ? math.cos(currentValue * math.pi / 180)
              : math.cos(currentValue);
          break;
        case 'tan':
          result = currentState.isDegreeMode
              ? math.tan(currentValue * math.pi / 180)
              : math.tan(currentValue);
          break;
        case 'ln':
          result = math.log(currentValue);
          break;
        case 'log':
          result = math.log(currentValue) / math.log(10);
          break;
        case 'sqrt':
          result = math.sqrt(currentValue);
          break;
        case '1/x':
          result = 1 / currentValue;
          break;
        case 'x²':
          result = currentValue * currentValue;
          break;
        case '!':
          result = _factorial(currentValue.toInt()).toDouble();
          break;
        default:
          return;
      }

      String displayResult;
      if (result == result.toInt()) {
        displayResult = result.toInt().toString();
      } else {
        displayResult = result.toString();
      }

      emit(ScientificCalculatorState(
        display: displayResult,
        expression: displayResult,
        shouldResetDisplay: true,
        isDegreeMode: currentState.isDegreeMode,
      ));
    } catch (e) {
      emit(ScientificCalculatorState(
        display: '0',
        expression: '',
        isDegreeMode: currentState.isDegreeMode,
      ));
    }
  }

  void _onToggleAngleModePressed(
      ToggleAngleModePressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();
    emit(currentState.copyWith(isDegreeMode: !currentState.isDegreeMode));
  }

  void _onConstantPressed(
      ConstantPressed event, Emitter<CalculatorState> emit) {
    final currentState = _getCurrentState();

    String constantValue;
    switch (event.constant) {
      case 'π':
        constantValue = math.pi.toString();
        break;
      case 'e':
        constantValue = math.e.toString();
        break;
      default:
        return;
    }

    if (currentState.shouldResetDisplay || currentState.display == '0') {
      emit(ScientificCalculatorState(
        display: constantValue,
        expression: constantValue,
        shouldResetDisplay: false,
        isDegreeMode: currentState.isDegreeMode,
      ));
    } else {
      emit(ScientificCalculatorState(
        display: currentState.display + constantValue,
        expression: currentState.expression + constantValue,
        isDegreeMode: currentState.isDegreeMode,
      ));
    }
  }

  bool _isOperator(String char) {
    return ['+', '-', '×', '÷', '*', '/'].contains(char);
  }

  void _calculatePower(
      String expression, Emitter<CalculatorState> emit, bool isDegreeMode) {
    try {
      List<String> parts = expression.split('^');
      if (parts.length == 2) {
        double base = double.parse(parts[0]);
        double exponent = double.parse(parts[1]);
        double result = math.pow(base, exponent).toDouble();

        String displayResult;
        if (result == result.toInt()) {
          displayResult = result.toInt().toString();
        } else {
          displayResult = result.toString();
        }

        emit(ScientificCalculatorState(
          display: displayResult,
          expression: displayResult,
          shouldResetDisplay: true,
          isDegreeMode: isDegreeMode,
        ));
      }
    } catch (e) {
      final currentState = _getCurrentState();
      emit(ScientificCalculatorState(
        display: '0',
        expression: '',
        isDegreeMode: currentState.isDegreeMode,
      ));
    }
  }

  int _factorial(int n) {
    if (n <= 1) return 1;
    return n * _factorial(n - 1);
  }
}
