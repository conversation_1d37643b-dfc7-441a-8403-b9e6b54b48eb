import 'package:equatable/equatable.dart';

abstract class CalculatorState extends Equatable {
  const CalculatorState();

  @override
  List<Object> get props => [];
}

class CalculatorInitial extends CalculatorState {
  const CalculatorInitial();
}

class CalculatorDisplayState extends CalculatorState {
  final String display;
  final String expression;
  final bool shouldResetDisplay;

  const CalculatorDisplayState({
    required this.display,
    required this.expression,
    this.shouldResetDisplay = false,
  });

  @override
  List<Object> get props => [display, expression, shouldResetDisplay];

  CalculatorDisplayState copyWith({
    String? display,
    String? expression,
    bool? shouldResetDisplay,
  }) {
    return CalculatorDisplayState(
      display: display ?? this.display,
      expression: expression ?? this.expression,
      shouldResetDisplay: shouldResetDisplay ?? this.shouldResetDisplay,
    );
  }
}

class CalculatorErrorState extends CalculatorState {
  final String message;

  const CalculatorErrorState(this.message);

  @override
  List<Object> get props => [message];
}

// Scientific calculator specific state
class ScientificCalculatorState extends CalculatorDisplayState {
  final bool isDegreeMode;

  const ScientificCalculatorState({
    required String display,
    required String expression,
    bool shouldResetDisplay = false,
    this.isDegreeMode = true,
  }) : super(
          display: display,
          expression: expression,
          shouldResetDisplay: shouldResetDisplay,
        );

  @override
  List<Object> get props => [display, expression, shouldResetDisplay, isDegreeMode];

  @override
  ScientificCalculatorState copyWith({
    String? display,
    String? expression,
    bool? shouldResetDisplay,
    bool? isDegreeMode,
  }) {
    return ScientificCalculatorState(
      display: display ?? this.display,
      expression: expression ?? this.expression,
      shouldResetDisplay: shouldResetDisplay ?? this.shouldResetDisplay,
      isDegreeMode: isDegreeMode ?? this.isDegreeMode,
    );
  }
}
