import 'package:calculator/constants/design.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class CurrencyExchangeScreen extends StatefulWidget {
  const CurrencyExchangeScreen({super.key});

  @override
  State<CurrencyExchangeScreen> createState() => _CurrencyExchangeScreenState();
}

class _CurrencyExchangeScreenState extends State<CurrencyExchangeScreen> {
  final _amountController = TextEditingController();
  String _fromCurrency = 'USD';
  String _toCurrency = 'CNY';
  double? _convertedAmount;
  double? _exchangeRate;
  bool _isLoading = false;
  
  final List<Map<String, String>> _currencies = [
    {'code': 'USD', 'name': 'US Dollar', 'symbol': '\$'},
    {'code': 'CNY', 'name': 'Chinese Yuan', 'symbol': '¥'},
    {'code': 'EUR', 'name': 'Euro', 'symbol': '€'},
    {'code': 'GBP', 'name': 'British Pound', 'symbol': '£'},
    {'code': 'JPY', 'name': 'Japanese Yen', 'symbol': '¥'},
    {'code': 'KRW', 'name': 'South Korean Won', 'symbol': '₩'},
    {'code': 'HKD', 'name': 'Hong Kong Dollar', 'symbol': 'HK\$'},
  ];

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _convertCurrency() async {
    final amount = double.tryParse(_amountController.text);
    
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的金额')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('http://localhost:8001/api/v1/exchange/convert'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'from_currency': _fromCurrency,
          'to_currency': _toCurrency,
          'amount': amount,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          _convertedAmount = data['converted_amount'];
          _exchangeRate = data['exchange_rate'];
        });
      } else {
        throw Exception('转换失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('转换失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _swapCurrencies() {
    setState(() {
      final temp = _fromCurrency;
      _fromCurrency = _toCurrency;
      _toCurrency = temp;
      _convertedAmount = null;
      _exchangeRate = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('汇率换算'),
        backgroundColor: MColor.skin,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 原始货币
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: DropdownButtonFormField<String>(
                            value: _fromCurrency,
                            decoration: const InputDecoration(
                              labelText: '原始货币',
                              border: OutlineInputBorder(),
                            ),
                            items: _currencies.map((currency) {
                              return DropdownMenuItem(
                                value: currency['code'],
                                child: Text('${currency['code']} - ${currency['name']}'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _fromCurrency = value!;
                                _convertedAmount = null;
                                _exchangeRate = null;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            controller: _amountController,
                            keyboardType: TextInputType.number,
                            decoration: const InputDecoration(
                              labelText: '金额',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 交换按钮
                    IconButton(
                      onPressed: _swapCurrencies,
                      icon: const Icon(Icons.swap_vert),
                      iconSize: 32,
                      color: Theme.of(context).primaryColor,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 目标货币
                    DropdownButtonFormField<String>(
                      value: _toCurrency,
                      decoration: const InputDecoration(
                        labelText: '目标货币',
                        border: OutlineInputBorder(),
                      ),
                      items: _currencies.map((currency) {
                        return DropdownMenuItem(
                          value: currency['code'],
                          child: Text('${currency['code']} - ${currency['name']}'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _toCurrency = value!;
                          _convertedAmount = null;
                          _exchangeRate = null;
                        });
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    ElevatedButton(
                      onPressed: _isLoading ? null : _convertCurrency,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : const Text(
                              '转换',
                              style: TextStyle(fontSize: 18),
                            ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            if (_convertedAmount != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        '转换结果',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '${_amountController.text} $_fromCurrency',
                            style: const TextStyle(fontSize: 18),
                          ),
                          const SizedBox(width: 16),
                          const Icon(Icons.arrow_forward),
                          const SizedBox(width: 16),
                          Text(
                            '${_convertedAmount!.toStringAsFixed(2)} $_toCurrency',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_exchangeRate != null)
                        Text(
                          '汇率: 1 $_fromCurrency = ${_exchangeRate!.toStringAsFixed(4)} $_toCurrency',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 20),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '说明',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    const Text('• 汇率数据每日更新'),
                    const Text('• 仅供参考，实际汇率以银行为准'),
                    const Text('• 支持主要国际货币转换'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
