import 'package:calculator/constants/design.dart';
import 'package:flutter/material.dart';

class RelativeCalculatorScreen extends StatefulWidget {
  const RelativeCalculatorScreen({super.key});

  @override
  State<RelativeCalculatorScreen> createState() => _RelativeCalculatorScreenState();
}

class _RelativeCalculatorScreenState extends State<RelativeCalculatorScreen> {
  String _myGender = '男';
  String _relativeGender = '男';
  String _relationship = '';
  String _result = '';
  String _reverseResult = '';
  
  // 亲戚关系数据库
  final Map<String, Map<String, String>> _relationshipData = {
    // 父系关系
    '父亲': {
      '男': '儿子',
      '女': '女儿',
    },
    '母亲': {
      '男': '儿子',
      '女': '女儿',
    },
    '爷爷': {
      '男': '孙子',
      '女': '孙女',
    },
    '奶奶': {
      '男': '孙子',
      '女': '孙女',
    },
    '太爷爷': {
      '男': '曾孙',
      '女': '曾孙女',
    },
    '太奶奶': {
      '男': '曾孙',
      '女': '曾孙女',
    },
    // 兄弟姐妹关系
    '哥哥': {
      '男': '弟弟',
      '女': '妹妹',
    },
    '姐姐': {
      '男': '弟弟',
      '女': '妹妹',
    },
    '弟弟': {
      '男': '哥哥',
      '女': '姐姐',
    },
    '妹妹': {
      '男': '哥哥',
      '女': '姐姐',
    },
    // 叔伯关系
    '叔叔': {
      '男': '侄子',
      '女': '侄女',
    },
    '伯伯': {
      '男': '侄子',
      '女': '侄女',
    },
    '姑姑': {
      '男': '侄子',
      '女': '侄女',
    },
    '舅舅': {
      '男': '外甥',
      '女': '外甥女',
    },
    '姨妈': {
      '男': '外甥',
      '女': '外甥女',
    },
    // 配偶关系
    '丈夫': {
      '女': '妻子',
    },
    '妻子': {
      '男': '丈夫',
    },
    // 岳父母/公婆关系
    '岳父': {
      '男': '女婿',
    },
    '岳母': {
      '男': '女婿',
    },
    '公公': {
      '女': '儿媳',
    },
    '婆婆': {
      '女': '儿媳',
    },
    // 孙辈关系
    '儿子': {
      '男': '父亲',
      '女': '母亲',
    },
    '女儿': {
      '男': '父亲',
      '女': '母亲',
    },
    '孙子': {
      '男': '爷爷',
      '女': '奶奶',
    },
    '孙女': {
      '男': '爷爷',
      '女': '奶奶',
    },
    '外孙': {
      '男': '外公',
      '女': '外婆',
    },
    '外孙女': {
      '男': '外公',
      '女': '外婆',
    },
    // 侄甥关系
    '侄子': {
      '男': '叔叔/伯伯',
      '女': '姑姑',
    },
    '侄女': {
      '男': '叔叔/伯伯',
      '女': '姑姑',
    },
    '外甥': {
      '男': '舅舅',
      '女': '姨妈',
    },
    '外甥女': {
      '男': '舅舅',
      '女': '姨妈',
    },
  };

  void _calculateRelationship() {
    if (_relationship.isEmpty) {
      setState(() {
        _result = '';
        _reverseResult = '';
      });
      return;
    }

    // 查找对应关系
    final relationData = _relationshipData[_relationship];
    if (relationData != null) {
      final result = relationData[_myGender];
      if (result != null) {
        setState(() {
          _result = result;
          // 计算反向关系
          _reverseResult = _calculateReverse(_relationship, _myGender);
        });
      } else {
        setState(() {
          _result = '无对应关系';
          _reverseResult = '';
        });
      }
    } else {
      setState(() {
        _result = '关系不存在';
        _reverseResult = '';
      });
    }
  }

  String _calculateReverse(String relationship, String myGender) {
    // 根据我的性别和对方的关系，计算我对对方的称呼
    final reverseData = _relationshipData[relationship];
    if (reverseData != null) {
      return reverseData[myGender] ?? '';
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('亲戚关系计算器'),
        backgroundColor: MColor.skin,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      '亲戚关系计算',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _myGender,
                            decoration: const InputDecoration(
                              labelText: '我的性别',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.person),
                            ),
                            items: const [
                              DropdownMenuItem(value: '男', child: Text('男')),
                              DropdownMenuItem(value: '女', child: Text('女')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _myGender = value!;
                                _calculateRelationship();
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _relativeGender,
                            decoration: const InputDecoration(
                              labelText: '对方性别',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.person_outline),
                            ),
                            items: const [
                              DropdownMenuItem(value: '男', child: Text('男')),
                              DropdownMenuItem(value: '女', child: Text('女')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _relativeGender = value!;
                                _calculateRelationship();
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _relationship.isEmpty ? null : _relationship,
                      decoration: const InputDecoration(
                        labelText: '对方是我的',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.family_restroom),
                        hintText: '请选择关系',
                      ),
                      items: _relationshipData.keys.map((String relationship) {
                        return DropdownMenuItem<String>(
                          value: relationship,
                          child: Text(relationship),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _relationship = value ?? '';
                          _calculateRelationship();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            if (_result.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        '计算结果',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.withOpacity(0.3)),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '我($_myGender)',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 20),
                                const Icon(Icons.arrow_forward, size: 24),
                                const SizedBox(width: 20),
                                Text(
                                  '$_relationship($_relativeGender)',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              '我应该叫对方：$_result',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                            if (_reverseResult.isNotEmpty) ...[
                              const SizedBox(height: 12),
                              Text(
                                '对方应该叫我：$_reverseResult',
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.green,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            const SizedBox(height: 20),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '使用说明',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 12),
                    const Text('• 选择您的性别和对方的性别'),
                    const Text('• 选择对方与您的关系'),
                    const Text('• 系统会自动计算出正确的称呼'),
                    const Text('• 支持常见的中国传统亲戚关系'),
                    const SizedBox(height: 12),
                    Text(
                      '支持的关系类型',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('直系：父母、祖父母、子女、孙辈'),
                    const Text('旁系：兄弟姐妹、叔伯姑舅姨'),
                    const Text('姻亲：配偶、岳父母、公婆'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
