import 'package:calculator/constants/design.dart';
import 'package:calculator/widgets/submit_button.dart';
import 'package:flutter/material.dart';

class InsuranceCalculatorScreen extends StatefulWidget {
  const InsuranceCalculatorScreen({super.key});

  @override
  State<InsuranceCalculatorScreen> createState() => _InsuranceCalculatorScreenState();
}

class _InsuranceCalculatorScreenState extends State<InsuranceCalculatorScreen> {
  final _salaryController = TextEditingController();
  String _city = '北京';
  
  double? _pensionPersonal;
  double? _pensionCompany;
  double? _medicalPersonal;
  double? _medicalCompany;
  double? _unemploymentPersonal;
  double? _unemploymentCompany;
  double? _workInjuryCompany;
  double? _maternityCompany;
  double? _housingFundPersonal;
  double? _housingFundCompany;
  double? _totalPersonal;
  double? _totalCompany;

  @override
  void dispose() {
    _salaryController.dispose();
    super.dispose();
  }

  void _calculateInsurance() {
    final salary = double.tryParse(_salaryController.text);
    
    if (salary != null && salary > 0) {
      // 北京地区五险一金比例（示例）
      setState(() {
        _pensionPersonal = salary * 0.08;
        _pensionCompany = salary * 0.16;
        
        _medicalPersonal = salary * 0.02 + 3;
        _medicalCompany = salary * 0.09;
        
        _unemploymentPersonal = salary * 0.002;
        _unemploymentCompany = salary * 0.008;
        
        _workInjuryCompany = salary * 0.004;
        _maternityCompany = salary * 0.008;
        
        _housingFundPersonal = salary * 0.12;
        _housingFundCompany = salary * 0.12;
        
        _totalPersonal = _pensionPersonal! + _medicalPersonal! + 
                        _unemploymentPersonal! + _housingFundPersonal!;
        _totalCompany = _pensionCompany! + _medicalCompany! + 
                       _unemploymentCompany! + _workInjuryCompany! + 
                       _maternityCompany! + _housingFundCompany!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('五险一金计算器'),
        backgroundColor: MColor.skin,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      DropdownButtonFormField<String>(
                        value: _city,
                        decoration: const InputDecoration(
                          labelText: '所在城市',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.location_city),
                        ),
                        items: const [
                          DropdownMenuItem(value: '北京', child: Text('北京')),
                          DropdownMenuItem(value: '上海', child: Text('上海')),
                          DropdownMenuItem(value: '广州', child: Text('广州')),
                          DropdownMenuItem(value: '深圳', child: Text('深圳')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _city = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _salaryController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '月工资 (元)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.attach_money),
                        ),
                      ),
                      const SizedBox(height: 20),
                      SubmitButton(
                        text: '计算五险一金',
                        onTap: _calculateInsurance,
                        
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              if (_totalPersonal != null) ...[
                Column(
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Text(
                              '个人缴费',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            _buildInsuranceItem('养老保险', _pensionPersonal!, '8%'),
                            _buildInsuranceItem('医疗保险', _medicalPersonal!, '2%+3元'),
                            _buildInsuranceItem('失业保险', _unemploymentPersonal!, '0.2%'),
                            _buildInsuranceItem('住房公积金', _housingFundPersonal!, '12%'),
                            const Divider(),
                            _buildTotalItem('个人合计', _totalPersonal!),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            Text(
                              '公司缴费',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 16),
                            _buildInsuranceItem('养老保险', _pensionCompany!, '16%'),
                            _buildInsuranceItem('医疗保险', _medicalCompany!, '9%'),
                            _buildInsuranceItem('失业保险', _unemploymentCompany!, '0.8%'),
                            _buildInsuranceItem('工伤保险', _workInjuryCompany!, '0.4%'),
                            _buildInsuranceItem('生育保险', _maternityCompany!, '0.8%'),
                            _buildInsuranceItem('住房公积金', _housingFundCompany!, '12%'),
                            const Divider(),
                            _buildTotalItem('公司合计', _totalCompany!),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInsuranceItem(String name, double amount, String rate) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(name),
          ),
          Expanded(
            child: Text(
              rate,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          Expanded(
            child: Text(
              '¥${amount.toStringAsFixed(2)}',
              style: const TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalItem(String name, double amount) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '¥${amount.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }
}
