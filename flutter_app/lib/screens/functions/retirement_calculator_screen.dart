import 'package:calculator/constants/design.dart';
import 'package:calculator/widgets/submit_button.dart';
import 'package:flutter/material.dart';

class RetirementCalculatorScreen extends StatefulWidget {
  const RetirementCalculatorScreen({super.key});

  @override
  State<RetirementCalculatorScreen> createState() => _RetirementCalculatorScreenState();
}

class _RetirementCalculatorScreenState extends State<RetirementCalculatorScreen> {
  final _birthYearController = TextEditingController();
  final _currentSalaryController = TextEditingController();
  final _workStartYearController = TextEditingController();

  String _gender = '男';
  String _jobType = '企业职工';
  int? _retirementAge;
  int? _retirementYear;
  int? _workingYears;
  double? _basicPension;
  double? _personalPension;
  double? _totalPension;

  @override
  void dispose() {
    _birthYearController.dispose();
    _currentSalaryController.dispose();
    _workStartYearController.dispose();
    super.dispose();
  }

  void _calculateRetirement() {
    final birthYear = int.tryParse(_birthYearController.text);
    final currentSalary = double.tryParse(_currentSalaryController.text);
    final workStartYear = int.tryParse(_workStartYearController.text);

    if (birthYear != null && currentSalary != null && workStartYear != null) {
      // 计算退休年龄
      int retirementAge;
      if (_jobType == '公务员' || _jobType == '事业单位') {
        retirementAge = _gender == '男' ? 60 : 55;
      } else {
        // 企业职工
        retirementAge = _gender == '男' ? 60 : 50;
        // 女干部55岁退休
        if (_gender == '女' && _jobType == '企业管理') {
          retirementAge = 55;
        }
      }

      final retirementYear = birthYear + retirementAge;
      final workingYears = retirementYear - workStartYear;

      // 计算养老金（简化计算）
      final averageSalary = currentSalary * 0.8; // 假设社会平均工资为当前工资的80%

      // 基础养老金 = (社会平均工资 + 个人指数化月平均缴费工资) ÷ 2 × 缴费年限 × 1%
      final basicPension = (averageSalary + currentSalary) / 2 * workingYears * 0.01;

      // 个人账户养老金 = 个人账户储存额 ÷ 计发月数
      // 假设个人账户累计 = 月工资 × 8% × 12 × 工作年限
      final personalAccount = currentSalary * 0.08 * 12 * workingYears;
      final monthsForCalculation = _getCalculationMonths(retirementAge);
      final personalPension = personalAccount / monthsForCalculation;

      setState(() {
        _retirementAge = retirementAge;
        _retirementYear = retirementYear;
        _workingYears = workingYears;
        _basicPension = basicPension;
        _personalPension = personalPension;
        _totalPension = basicPension + personalPension;
      });
    }
  }

  int _getCalculationMonths(int retirementAge) {
    // 计发月数表（简化版）
    switch (retirementAge) {
      case 50: return 195;
      case 55: return 170;
      case 60: return 139;
      case 65: return 101;
      default: return 139;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('退休计算器'),
        backgroundColor: MColor.skin,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _gender,
                              decoration: const InputDecoration(
                                labelText: '性别',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.person),
                              ),
                              items: const [
                                DropdownMenuItem(value: '男', child: Text('男')),
                                DropdownMenuItem(value: '女', child: Text('女')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _gender = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _jobType,
                              decoration: const InputDecoration(
                                labelText: '职业类型',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.work),
                              ),
                              items: const [
                                DropdownMenuItem(value: '企业职工', child: Text('企业职工')),
                                DropdownMenuItem(value: '企业管理', child: Text('企业管理')),
                                DropdownMenuItem(value: '公务员', child: Text('公务员')),
                                DropdownMenuItem(value: '事业单位', child: Text('事业单位')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _jobType = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _birthYearController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '出生年份',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.cake),
                          hintText: '例如：1980',
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _workStartYearController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '参加工作年份',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.work_history),
                          hintText: '例如：2000',
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _currentSalaryController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '当前月工资 (元)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.attach_money),
                        ),
                      ),
                      const SizedBox(height: 20),
                      SubmitButton(
                        text: '计算退休信息',
                        onTap: _calculateRetirement,
                        
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              if (_retirementAge != null) ...[
                Column(
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Text(
                              '退休信息',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 20),
                            _buildResultItem(
                              '退休年龄',
                              '$_retirementAge岁',
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            _buildResultItem(
                              '退休年份',
                              '$_retirementYear年',
                              Colors.green,
                            ),
                            const SizedBox(height: 12),
                            _buildResultItem(
                              '工作年限',
                              '$_workingYears年',
                              Colors.orange,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Text(
                              '预估养老金',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 20),
                            _buildResultItem(
                              '基础养老金',
                              '¥${_basicPension!.toStringAsFixed(2)}',
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            _buildResultItem(
                              '个人账户养老金',
                              '¥${_personalPension!.toStringAsFixed(2)}',
                              Colors.green,
                            ),
                            const Divider(height: 24),
                            _buildResultItem(
                              '月养老金总额',
                              '¥${_totalPension!.toStringAsFixed(2)}',
                              Colors.red,
                              isMain: true,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '说明',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 12),
                            const Text('• 此计算结果仅供参考，实际养老金以社保部门核定为准'),
                            const Text('• 计算基于当前政策，未来政策可能调整'),
                            const Text('• 养老金会根据社会平均工资和物价水平调整'),
                            const Text('• 建议提前规划，适当补充商业养老保险'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultItem(String label, String value, Color color, {bool isMain = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isMain ? 18 : 16,
            fontWeight: isMain ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isMain ? 24 : 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
