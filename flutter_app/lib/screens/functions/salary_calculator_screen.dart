import 'package:calculator/constants/design.dart';
import 'package:calculator/widgets/submit_button.dart';
import 'package:flutter/material.dart';

class SalaryCalculatorScreen extends StatefulWidget {
  const SalaryCalculatorScreen({super.key});

  @override
  State<SalaryCalculatorScreen> createState() => _SalaryCalculatorScreenState();
}

class _SalaryCalculatorScreenState extends State<SalaryCalculatorScreen> {
  final _grossSalaryController = TextEditingController();
  final _socialInsuranceController = TextEditingController();
  final _housingFundController = TextEditingController();
  
  double? _netSalary;
  double? _personalTax;
  double? _totalDeductions;
  String _city = '北京';

  @override
  void dispose() {
    _grossSalaryController.dispose();
    _socialInsuranceController.dispose();
    _housingFundController.dispose();
    super.dispose();
  }

  void _calculateNetSalary() {
    final grossSalary = double.tryParse(_grossSalaryController.text);
    final socialInsurance = double.tryParse(_socialInsuranceController.text) ?? 0;
    final housingFund = double.tryParse(_housingFundController.text) ?? 0;

    if (grossSalary != null && grossSalary > 0) {
      // 个税起征点
      const taxThreshold = 5000.0;
      
      // 计算应纳税所得额
      final taxableIncome = grossSalary - socialInsurance - housingFund - taxThreshold;
      
      // 计算个人所得税
      double personalTax = 0;
      if (taxableIncome > 0) {
        personalTax = _calculatePersonalTax(taxableIncome);
      }
      
      setState(() {
        _personalTax = personalTax;
        _totalDeductions = socialInsurance + housingFund + personalTax;
        _netSalary = grossSalary - _totalDeductions!;
      });
    }
  }

  double _calculatePersonalTax(double taxableIncome) {
    double tax = 0;
    
    if (taxableIncome <= 3000) {
      tax = taxableIncome * 0.03;
    } else if (taxableIncome <= 12000) {
      tax = 3000 * 0.03 + (taxableIncome - 3000) * 0.10;
    } else if (taxableIncome <= 25000) {
      tax = 3000 * 0.03 + 9000 * 0.10 + (taxableIncome - 12000) * 0.20;
    } else if (taxableIncome <= 35000) {
      tax = 3000 * 0.03 + 9000 * 0.10 + 13000 * 0.20 + (taxableIncome - 25000) * 0.25;
    } else if (taxableIncome <= 55000) {
      tax = 3000 * 0.03 + 9000 * 0.10 + 13000 * 0.20 + 10000 * 0.25 + (taxableIncome - 35000) * 0.30;
    } else if (taxableIncome <= 80000) {
      tax = 3000 * 0.03 + 9000 * 0.10 + 13000 * 0.20 + 10000 * 0.25 + 20000 * 0.30 + (taxableIncome - 55000) * 0.35;
    } else {
      tax = 3000 * 0.03 + 9000 * 0.10 + 13000 * 0.20 + 10000 * 0.25 + 20000 * 0.30 + 25000 * 0.35 + (taxableIncome - 80000) * 0.45;
    }
    
    return tax;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('税后工资计算器'),
        backgroundColor: MColor.skin,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      DropdownButtonFormField<String>(
                        value: _city,
                        decoration: const InputDecoration(
                          labelText: '所在城市',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.location_city),
                        ),
                        items: const [
                          DropdownMenuItem(value: '北京', child: Text('北京')),
                          DropdownMenuItem(value: '上海', child: Text('上海')),
                          DropdownMenuItem(value: '广州', child: Text('广州')),
                          DropdownMenuItem(value: '深圳', child: Text('深圳')),
                          DropdownMenuItem(value: '其他', child: Text('其他')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _city = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _grossSalaryController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '税前工资 (元)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.attach_money),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _socialInsuranceController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '五险一金 (元)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.security),
                          hintText: '可选，不填写将使用默认比例计算',
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: _housingFundController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '专项扣除 (元)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.home),
                          hintText: '住房贷款利息、子女教育等',
                        ),
                      ),
                      const SizedBox(height: 20),
                      SubmitButton(
                        text: '计算税后工资',
                        onTap: _calculateNetSalary,
                        
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              if (_netSalary != null) ...[
                Column(
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Text(
                              '计算结果',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 20),
                            _buildResultItem(
                              '税后工资',
                              '¥${_netSalary!.toStringAsFixed(2)}',
                              Colors.green,
                              isMain: true,
                            ),
                            const Divider(height: 24),
                            _buildResultItem(
                              '个人所得税',
                              '¥${_personalTax!.toStringAsFixed(2)}',
                              Colors.red,
                            ),
                            const SizedBox(height: 8),
                            _buildResultItem(
                              '总扣除额',
                              '¥${_totalDeductions!.toStringAsFixed(2)}',
                              Colors.orange,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '个税税率表（月度）',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 12),
                            _buildTaxBracket('0 - 3,000', '3%'),
                            _buildTaxBracket('3,000 - 12,000', '10%'),
                            _buildTaxBracket('12,000 - 25,000', '20%'),
                            _buildTaxBracket('25,000 - 35,000', '25%'),
                            _buildTaxBracket('35,000 - 55,000', '30%'),
                            _buildTaxBracket('55,000 - 80,000', '35%'),
                            _buildTaxBracket('80,000以上', '45%'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultItem(String label, String value, Color color, {bool isMain = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isMain ? 18 : 16,
            fontWeight: isMain ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isMain ? 24 : 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildTaxBracket(String range, String rate) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(range),
          Text(
            rate,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
