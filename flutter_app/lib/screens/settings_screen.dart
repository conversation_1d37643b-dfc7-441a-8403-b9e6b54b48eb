import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../router/app_router.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _version = '';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _version = packageInfo.version;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // App Logo and Version
          Container(
            padding: const EdgeInsets.all(32),
            child: Column(
              children: [
                // Container(
                //   width: 80,
                //   height: 80,
                //   decoration: BoxDecoration(
                //     color: Theme.of(context).colorScheme.primary,
                //     borderRadius: BorderRadius.circular(20),
                //   ),
                //   child: Icon(
                //     Icons.calculate,
                //     size: 40,
                //     color: Theme.of(context).colorScheme.onPrimary,
                //   ),
                // ),
                Container(
                                    decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Image.asset('assets/images/ic_launcher.png', width: 80, height: 80,)),
                const SizedBox(height: 16),
                Text(
                  '万算王全能计算器',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '版本 $_version',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Settings List
          _buildSettingsItem(
            context,
            icon: Icons.privacy_tip,
            title: '隐私政策',
            onTap: () => AppNavigation.goToWeb(context, 'https://calculator.ceweng.com/privacy', title: '隐私政策'),
          ),
          
          _buildSettingsItem(
            context,
            icon: Icons.description,
            title: '用户协议',
            onTap: () => AppNavigation.goToWeb(context, 'https://calculator.ceweng.com/useragreement', title: '用户协议'),
          ),
          
          _buildSettingsItem(
            context,
            icon: Icons.clear_all,
            title: '清除缓存',
            onTap: () {
              // TODO: Clear cache
              _showClearCacheDialog(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon),
        title: Text(title),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature),
        content: const Text('此功能即将推出，敬请期待！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除缓存'),
        content: const Text('确定要清除应用缓存吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('缓存已清除')),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
