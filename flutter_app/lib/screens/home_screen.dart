import 'package:calculator/constants/design.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../router/app_router.dart';

class HomeScreen extends StatefulWidget {
  final StatefulNavigationShell navigationShell;
  const HomeScreen(
    this.navigationShell, {
    Key? key,
  }) : super(key: key ?? const ValueKey<String>('HomeScreen'));
  @override
  State createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<String> _titles = [
    '标准',
    '科学',
    '功能',
    '设置',
  ];

  final List<String> _routes = [
    '/standard',
    '/scientific',
    '/functions',
    '/settings',
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateCurrentIndex();
  }

  void _updateCurrentIndex() {
    final String location = GoRouterState.of(context).uri.path;
    final int index = _routes.indexOf(location);
    if (index != -1 && index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  String _getCurrentTitle() {
    final String location = GoRouterState.of(context).uri.path;
    final int index = _routes.indexOf(location);
    return index != -1 ? _titles[index] : '计算器';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getCurrentTitle()),
        backgroundColor: MColor.skin,
      ),
      body: widget.navigationShell,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          switch (index) {
            case 0:
              AppNavigation.goToStandard(context);
              break;
            case 1:
              AppNavigation.goToScientific(context);
              break;
            case 2:
              AppNavigation.goToFunctions(context);
              break;
            case 3:
              AppNavigation.goToSettings(context);
              break;
          }
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.calculate),
            label: '标准',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.functions),
            label: '科学',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.apps),
            label: '功能',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: '设置',
          ),
        ],
      ),
    );
  }
}
