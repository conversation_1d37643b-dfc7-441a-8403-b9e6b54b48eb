import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/standard_calculator_bloc.dart';
import '../blocs/calculator_event.dart';
import '../blocs/calculator_state.dart';
import '../widgets/calculator_button.dart';
import '../widgets/calculator_display.dart';

class StandardCalculatorScreen extends StatelessWidget {
  const StandardCalculatorScreen({super.key});

  // 处理括号输入的逻辑
  void _handleBrackets(StandardCalculatorBloc bloc, CalculatorDisplayState state) {
    String expression = state.expression;

    // 计算当前表达式中左括号和右括号的数量
    int leftBrackets = expression.split('(').length - 1;
    int rightBrackets = expression.split(')').length - 1;

    // 如果左括号多于右括号，或者表达式为空，或者最后一个字符是运算符，则输入左括号
    if (leftBrackets > rightBrackets &&
        expression.isNotEmpty &&
        !_isOperator(expression[expression.length - 1])) {
      bloc.add(const OperatorPressed(')'));
    } else {
      bloc.add(const OperatorPressed('('));
    }
  }

  // 判断字符是否为运算符
  bool _isOperator(String char) {
    return ['+', '-', '×', '÷', '*', '/', '^'].contains(char);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StandardCalculatorBloc, CalculatorState>(
      builder: (context, state) {
        if (state is CalculatorErrorState) {
          return Center(
            child: Text(
              state.message,
              style: const TextStyle(color: Colors.red, fontSize: 24),
            ),
          );
        }

        final displayState = state is CalculatorDisplayState
            ? state
            : const CalculatorDisplayState(display: '0', expression: '');

        final bloc = context.read<StandardCalculatorBloc>();

        return Column(
          children: [
            // Display area (1/3 of screen)
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: CalculatorDisplay(
                  display: displayState.display,
                  expression: displayState.expression,
                ),
              ),
            ),
            
            // Button area (2/3 of screen) - 5行4列网格布局
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Column(
                  children: [
                    // 第一行: C、÷、×、DEL
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'C',
                              onPressed: () => bloc.add(const ClearPressed()),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '÷',
                              onPressed: () => bloc.add(const OperatorPressed('÷')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '×',
                              onPressed: () => bloc.add(const OperatorPressed('×')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: 'DEL',
                              onPressed: () => bloc.add(const DeletePressed()),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),

                    // 第二行: 7、8、9、-
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: '7',
                              onPressed: () => bloc.add(const NumberPressed('7')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '8',
                              onPressed: () => bloc.add(const NumberPressed('8')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '9',
                              onPressed: () => bloc.add(const NumberPressed('9')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '-',
                              onPressed: () => bloc.add(const OperatorPressed('-')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),

                    // 第三行: 4、5、6、+
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: '4',
                              onPressed: () => bloc.add(const NumberPressed('4')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '5',
                              onPressed: () => bloc.add(const NumberPressed('5')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '6',
                              onPressed: () => bloc.add(const NumberPressed('6')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '+',
                              onPressed: () => bloc.add(const OperatorPressed('+')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),

                    // 第四行: 1、2、3、()
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: '1',
                              onPressed: () => bloc.add(const NumberPressed('1')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '2',
                              onPressed: () => bloc.add(const NumberPressed('2')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '3',
                              onPressed: () => bloc.add(const NumberPressed('3')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '( )',
                              onPressed: () => _handleBrackets(bloc, displayState),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),

                    // 第五行: %、0、.、=
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: '%',
                              onPressed: () => bloc.add(const OperatorPressed('%')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '0',
                              onPressed: () => bloc.add(const NumberPressed('0')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '.',
                              onPressed: () => bloc.add(const DecimalPressed()),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: CalculatorButton(
                              text: '=',
                              onPressed: () => bloc.add(const EqualsPressed()),
                              backgroundColor: Colors.orange,
                              textColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
