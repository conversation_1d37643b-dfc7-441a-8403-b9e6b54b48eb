import 'package:calculator/blocs/calculator_state.dart';
import 'package:calculator/blocs/scientific_calculator_bloc.dart';
import 'package:calculator/blocs/calculator_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../widgets/calculator_button.dart';
import '../widgets/calculator_display.dart';

class ScientificCalculatorScreen extends StatelessWidget {
  const ScientificCalculatorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ScientificCalculatorBloc, CalculatorState>(
      builder: (context, state) {
        // 确保状态是ScientificCalculatorState类型
        final scientificState = state is ScientificCalculatorState
            ? state
            : const ScientificCalculatorState(display: '0', expression: '');

        return Column(
          children: [
            // Display area (1/3 of screen)
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: CalculatorDisplay(
                  display: scientificState.display,
                  expression: scientificState.expression,
                ),
              ),
            ),
            
            // Button area (2/3 of screen) - 7行5列网格布局
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.all(4),
                child: Column(
                  children: [
                    // 第一行: C、deg、fx、1/X、e
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'C',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ClearPressed()),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: scientificState.isDegreeMode ? 'deg' : 'rad',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ToggleAngleModePressed()),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'fx',
                              onPressed: () {}, // 预留功能
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '1/x',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('reciprocal')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'e',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ConstantPressed('e')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第二行: sin、√、xʸ、x!、π
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'sin',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('sin')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '√',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('sqrt')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'xʸ',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed('^')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'x!',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('factorial')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'π',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ConstantPressed('π')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第三行: cos、(、)、DEL、÷
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'cos',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('cos')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '(',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed('(')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: ')',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed(')')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: 'DEL',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const DeletePressed()),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '÷',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed('÷')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第四行: tan、7、8、9、×
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'tan',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('tan')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '7',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('7')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '8',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('8')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '9',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('9')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '×',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed('×')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第五行: cot、4、5、6、-
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'cot',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('cot')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '4',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('4')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '5',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('5')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '6',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('6')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '-',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed('-')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第六行: ln、1、2、3、+
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'ln',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('ln')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '1',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('1')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '2',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('2')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '3',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('3')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '+',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed('+')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // 第七行: lg、%、0、.、=
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: CalculatorButton(
                              text: 'lg',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const ScientificFunctionPressed('log')),
                              backgroundColor: Colors.white,
                              textColor: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '%',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const OperatorPressed('%')),
                              backgroundColor: Colors.white,
                              textColor: Colors.orange,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '0',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const NumberPressed('0')),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '.',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const DecimalPressed()),
                              backgroundColor: Colors.white,
                              textColor: Colors.black,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: CalculatorButton(
                              text: '=',
                              onPressed: () => context.read<ScientificCalculatorBloc>().add(const EqualsPressed()),
                              backgroundColor: Colors.orange,
                              textColor: Colors.white,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
