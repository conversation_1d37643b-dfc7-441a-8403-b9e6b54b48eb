import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../screens/home_screen.dart';
import '../screens/standard_calculator_screen.dart';
import '../screens/scientific_calculator_screen.dart';
import '../screens/functions_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/web_screen.dart';
import '../screens/functions/mortgage_calculator_screen.dart';
import '../screens/functions/salary_calculator_screen.dart';
import '../screens/functions/bmi_calculator_screen.dart';
import '../screens/functions/insurance_calculator_screen.dart';
import '../screens/functions/tax_calculator_screen.dart';
import '../screens/functions/retirement_calculator_screen.dart';
import '../screens/functions/currency_exchange_screen.dart';
import '../screens/functions/relative_calculator_screen.dart';

class AppRouter {

  static final GlobalKey<NavigatorState> parentNavigatorKey = GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _sectionANavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'sectionANav');

  static Page getPage({
    required Widget child,
    required GoRouterState state,
  }) {
    return MaterialPage(
      key: state.pageKey,
      child: child,
    );
  }

  static final GoRouter router = GoRouter(
      navigatorKey: parentNavigatorKey,
    initialLocation: '/standard',
    routes: [
      StatefulShellRoute.indexedStack(
        parentNavigatorKey: parentNavigatorKey,
        branches: [
          StatefulShellBranch(
            navigatorKey: _sectionANavigatorKey,
            routes: <RouteBase>[
              GoRoute(
                path: '/standard',
                builder: (context, state) => const StandardCalculatorScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: '/scientific',
                builder: (context, state) => const ScientificCalculatorScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: '/functions',
                builder: (context, state) => const FunctionsScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: <RouteBase>[
              GoRoute(
                path: '/settings',
                builder: (context, state) => const SettingsScreen(),
              ),
            ],
          ),
        ],
        pageBuilder: (
          BuildContext context,
          GoRouterState state,
          StatefulNavigationShell navigationShell,
        ) {
          return getPage(
            child: HomeScreen(
              navigationShell,
            ),
            state: state,
          );
        },
        // builder: (context, state, child) {
        //   return HomeScreen(child: child);
        // },
      ),
      // Function calculator routes
      GoRoute(
        path: '/mortgage',
        name: 'mortgage',
        builder: (context, state) => const MortgageCalculatorScreen(),
      ),
      GoRoute(
        path: '/salary',
        name: 'salary',
        builder: (context, state) => const SalaryCalculatorScreen(),
      ),
      GoRoute(
        path: '/bmi',
        name: 'bmi',
        builder: (context, state) => const BMICalculatorScreen(),
      ),
      GoRoute(
        path: '/insurance',
        name: 'insurance',
        builder: (context, state) => const InsuranceCalculatorScreen(),
      ),
      GoRoute(
        path: '/tax',
        name: 'tax',
        builder: (context, state) => const TaxCalculatorScreen(),
      ),
      GoRoute(
        path: '/retirement',
        name: 'retirement',
        builder: (context, state) => const RetirementCalculatorScreen(),
      ),
      GoRoute(
        path: '/currency',
        name: 'currency',
        builder: (context, state) => const CurrencyExchangeScreen(),
      ),
      GoRoute(
        path: '/relative',
        name: 'relative',
        builder: (context, state) => const RelativeCalculatorScreen(),
      ),
      // Web page route
      GoRoute(
        path: '/web',
        name: 'web',
        builder: (context, state) {
          final url = state.uri.queryParameters['url'] ?? '';
          final title = state.uri.queryParameters['title'] ?? 'Web Page';
          return WebPage(url, title: title);
        },
      ),
    ],
  );
}

// Navigation helper class
class AppNavigation {
  static void goToStandard(BuildContext context) {
    context.go('/standard');
  }

  static void goToScientific(BuildContext context) {
    context.go('/scientific');
  }

  static void goToFunctions(BuildContext context) {
    context.go('/functions');
  }

  static void goToSettings(BuildContext context) {
    context.go('/settings');
  }

  static void goToMortgage(BuildContext context) {
    context.push('/mortgage');
  }

  static void goToSalary(BuildContext context) {
    context.push('/salary');
  }

  static void goToBMI(BuildContext context) {
    context.push('/bmi');
  }

  static void goToInsurance(BuildContext context) {
    context.push('/insurance');
  }

  static void goToTax(BuildContext context) {
    context.push('/tax');
  }

  static void goToRetirement(BuildContext context) {
    context.push('/retirement');
  }

  static void goToCurrency(BuildContext context) {
    context.push('/currency');
  }

  static void goToRelative(BuildContext context) {
    context.push('/relative');
  }

  static void goToWeb(BuildContext context, String url, {String title = 'Web Page'}) {
    context.push('/web?url=${Uri.encodeComponent(url)}&title=${Uri.encodeComponent(title)}');
  }
}
