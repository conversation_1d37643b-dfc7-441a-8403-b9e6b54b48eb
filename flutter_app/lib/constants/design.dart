// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

///颜色
class MColor {
  MColor._();

  ///主题色
  static const Color skin = Color(0xFF5CB1AC);
  static const Color lightSkin = Color.fromARGB(255, 173, 239, 234);
  static const Color xFF5CB188 = Color(0xFF5CB188);
  static const Color xFF1C6974 = Color(0xFF1C6974);
  static const Color xFFD05363 = Color(0xFFD05363);
  static const Color xFFF5D16D = Color(0xFFF5D16D);
  static const Color xFFFB9186 = Color(0xFFFB9186);
  static const Color xFF315C5B = Color(0xFF315C5B);
  static const Color xFFD7F5E6 = Color(0xFFD7F5E6);
  static const Color xFF4C9B93 = Color(0xFF4C9B93);
  static const Color xFFC2E2CF = Color(0xFFC2E2CF);
  static const Color xFFFED58E = Color(0xFFFED58E);
  static const Color xFFF6CB86 = Color(0xFFF6CB86);
  static const Color xFFFF7858 = Color(0xFFFF7858);
  static const Color xFF333333 = Color(0xFF333333);
  static const Color xFFFFFFFF = Color(0xFFFFFFFF);
  static const Color xFF000000 = Color(0xFF000000);
  static const Color xFF999999 = Color(0xFF979797);
  static const Color xFFEEEEEE = Color(0xFFEEEEEE);
  static const Color xFF777777 = Color(0xFF777777);
  static const Color xFFECECEC = Color(0xFFECECEC);
  static const Color xFF1B1C1A = Color(0xFF1B1C1A);
  static const Color xFF2E2C24 = Color(0xFF2E2C24);
  static const Color xFFD9D9D9 = Color(0xFFD9D9D9);
  static const Color xFFF5F5F5 = Color(0xFFF5F5F5);
  static const Color xFFF0F0F0 = Color(0xFFF0F0F0);
  static const Color xFFFFD180 = Color(0xFFFFD180);
  static const Color xFFF4FEFA = Color(0xFFF4FEFA);
  static const Color xFF553600 = Color(0xFF553600);
  static const Color xFF895C23 = Color(0xFF895C23);
  static const Color xFFFFEBC4 = Color(0xFFFFEBC4);
  static const Color xFFE8E8E8 = Color(0xFFE8E8E8);
  static const Color xFFCB322E = Color(0xFFCB322E);
  static const Color xFFFF918D = Color(0xFFFF918D);
  static const Color xFFED726E = Color(0xFFED726E);
  static const Color xFFFFF4E1 = Color(0xFFFFF4E1);
  static const Color xFFFFBE4A = Color(0xFFFFBE4A);
  static const Color xFF68C2BF = Color(0xFF68C2BF);
  static const Color xFF8C8C8C = Color(0xFF8C8C8C);
  static const Color xFFEBEBEB = Color(0xFFEBEBEB);
  static const Color xFF577F8C = Color(0xFF577F8C);
  static const Color xFF99CEC2 = Color(0xFF99CEC2);
  static const Color xFFAE6C49 = Color(0xFFAE6C49);
  static const Color xFFD6AC94 = Color(0xFFD6AC94);
  static const Color xFFF5CDA8 = Color(0xFFF5CDA8);
  static const Color xFFE0A455 = Color(0xFFE0A455);
  static const Color xFFF1E4D4 = Color(0xFFF1E4D4);
}

class MFont {
  static const bold30 = TextStyle(fontSize: 30, fontWeight: FontWeight.w700);
  static const bold24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w700);
  static const bold22 = TextStyle(fontSize: 22, fontWeight: FontWeight.w700);
  static const bold20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w700);
  static const bold16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w700);

  static const semi_Bold24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w600);
  static const semi_Bold20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w600);
  static const semi_Bold17 = TextStyle(fontSize: 17, fontWeight: FontWeight.w600);
  static const semi_Bold16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w600);
  static const semi_Bold15 = TextStyle(fontSize: 15, fontWeight: FontWeight.w600);
  static const semi_Bold14 = TextStyle(fontSize: 14, fontWeight: FontWeight.w600);
  static const semi_Bold13 = TextStyle(fontSize: 13, fontWeight: FontWeight.w600);
  static const semi_Bold12 = TextStyle(fontSize: 12, fontWeight: FontWeight.w600);
  static const semi_Bold11 = TextStyle(fontSize: 11, fontWeight: FontWeight.w600);

  ///medium
  static const medium36 = TextStyle(fontSize: 36, fontWeight: FontWeight.w500);
  static const medium30 = TextStyle(fontSize: 30, fontWeight: FontWeight.w500);
  static const medium26 = TextStyle(fontSize: 26, fontWeight: FontWeight.w500);
  static const medium24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w500);
  static const medium20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w500);
  static const medium18 = TextStyle(fontSize: 18, fontWeight: FontWeight.w500);
  static const medium17 = TextStyle(fontSize: 17, fontWeight: FontWeight.w500);
  static const medium16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w500);
  static const medium15 = TextStyle(fontSize: 15, fontWeight: FontWeight.w500);
  static const medium14 = TextStyle(fontSize: 14, fontWeight: FontWeight.w500);
  static const medium13 = TextStyle(fontSize: 13, fontWeight: FontWeight.w500);
  static const medium12 = TextStyle(fontSize: 12, fontWeight: FontWeight.w500);
  static const medium11 = TextStyle(fontSize: 11, fontWeight: FontWeight.w500);
  static const medium10 = TextStyle(fontSize: 10, fontWeight: FontWeight.w500);

  ///regular
  static const regular30 = TextStyle(fontSize: 30, fontWeight: FontWeight.w400);
  static const regular24 = TextStyle(fontSize: 24, fontWeight: FontWeight.w400);
  static const regular20 = TextStyle(fontSize: 20, fontWeight: FontWeight.w400);
  static const regular19 = TextStyle(fontSize: 19, fontWeight: FontWeight.w400);
  static const regular18 = TextStyle(fontSize: 18, fontWeight: FontWeight.w400);
  static const regular17 = TextStyle(fontSize: 17, fontWeight: FontWeight.w400);
  static const regular16 = TextStyle(fontSize: 16, fontWeight: FontWeight.w400);
  static const regular15 = TextStyle(fontSize: 15, fontWeight: FontWeight.w400);
  static const regular14 = TextStyle(fontSize: 14, fontWeight: FontWeight.w400);
  static const regular13 = TextStyle(fontSize: 13, fontWeight: FontWeight.w400);
  static const regular12 = TextStyle(fontSize: 12, fontWeight: FontWeight.w400);
  static const regular11 = TextStyle(fontSize: 11, fontWeight: FontWeight.w400);
  static const regular10 = TextStyle(fontSize: 10, fontWeight: FontWeight.w400);
  static const regular6 = TextStyle(fontSize: 6, fontWeight: FontWeight.w400);
}
