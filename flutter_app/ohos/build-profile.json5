{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "storeFile": "/Volumes/Disk0/Projects/qiazhun/ohos/key.p12",
          "storePassword": "0000001AE880F185533CDF69F31F41DE77E6405C75CAAA482CFBA400D16EBE676A36B76116E7F5D82664",
          "keyAlias": "qiazhun0",
          "keyPassword": "0000001A398AD13908EB3FAD9EB9BA1246E109B6B7DE48382DEAB89964AB09051BDF52F459CCE33D0EAC",
          "signAlg": "SHA256withECDSA",
          "profile": "/Volumes/Disk0/Projects/calculator-master/flutter_app/ohos/calculatorRelease.p7b",
          "certpath": "/Volumes/Disk0/Projects/qiazhun/ohos/Publish.cer"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.1.0(18)",
        "runtimeOS": "HarmonyOS",
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "profile"
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}